/**
 * ✅ MINIMAL EFFICIENCY HELPERS
 * 
 * Only contains complex operations that would otherwise be duplicated.
 * Keeps focused functions clean while preventing waste.
 */

/**
 * Deep equality check for stats objects (prevents unnecessary stats writes)
 */
export function statsEqual(stats1: any, stats2: any): boolean {
  if (!stats1 && !stats2) return true;
  if (!stats1 || !stats2) return false;
  
  // Compare key numeric fields that matter for stats
  const keys = [
    'deliveryCount', 'tipCount', 'totalTips', 'pendingCount', 'averageTipAmount', 'highestTip',
    'totalBasePay', 'totalFinalPay', 'verifiedDeliveryCount', 'cancelledDeliveryCount'
  ];
  
  for (const key of keys) {
    const val1 = stats1[key] ?? 0;
    const val2 = stats2[key] ?? 0;
    
    // Handle floating point comparison for monetary values
    if (key === 'totalTips' || key === 'averageTipAmount' || key === 'highestTip' || 
        key === 'totalBasePay' || key === 'totalFinalPay') {
      if (Math.abs(val1 - val2) > 0.01) return false;
    } else {
      if (val1 !== val2) return false;
    }
  }
  
  // Compare timestamp fields (convert to milliseconds for comparison)
  const timestampKeys = ['lastDeliveryTimestamp'];
  for (const key of timestampKeys) {
    const ts1 = stats1[key]?.toMillis?.() ?? stats1[key] ?? 0;
    const ts2 = stats2[key]?.toMillis?.() ?? stats2[key] ?? 0;
    if (ts1 !== ts2) return false;
  }
  
  // Compare object fields (platform/currency breakdowns)
  const objectKeys = ['platformBreakdown', 'currencyBreakdown'];
  for (const key of objectKeys) {
    const obj1 = stats1[key] ?? {};
    const obj2 = stats2[key] ?? {};
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    for (const subKey of keys1) {
      if (obj1[subKey] !== obj2[subKey]) return false;
    }
  }
  
  return true;
}

/**
 * Check if deltas contain significant changes (prevents unnecessary user profile updates)
 */
export function hasSignificantDeltas(deltas: any): boolean {
  return Math.abs(deltas.deliveryCount || 0) > 0 ||
         Math.abs(deltas.tipCount || 0) > 0 ||
         Math.abs(deltas.totalTips || 0) > 0.01 ||
         Math.abs(deltas.pendingCount || 0) > 0 ||
         deltas.dndCreated ||
         deltas.dndRemoved;
}

/**
 * Check if user profile update data contains real changes (not just metadata)
 */
export function hasRealUserUpdates(updateData: any): boolean {
  return Object.keys(updateData).some(key => 
    key.startsWith('usage.') || 
    key.startsWith('usageStats.') ||
    key.includes('dndMarkingsUsed') ||
    key.includes('featureUsage')
  );
}

/**
 * Log efficiency gains for monitoring
 */
export function logEfficiencyGain(logPrefix: string, operation: string, skipped: boolean, reason?: string) {
  if (skipped) {
    console.log(`${logPrefix} ⚡ EFFICIENCY: Skipped ${operation} - ${reason || 'no changes detected'}`);
  }
}
