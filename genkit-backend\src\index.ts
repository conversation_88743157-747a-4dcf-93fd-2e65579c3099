// Initialization is now handled in local-runner.ts for local testing,
// and implicitly by the Cloud Functions environment upon deployment.

// Re-export triggers and callable functions from the dedicated triggers file
// This makes them discoverable by the Firebase Functions deployment process.
export {
    onDeliveryWrittenUpdateAddressStats, // ✅ CORRECT: Uses proper domain model with tipAmount=null for pending
    testParseImportFlow,
    onGeoJsonFileFinalized,
    // Manual DND override callable function
    setManualAddressDndOverride,
    // ✅ FIRESTORE-ONLY: User preferences change trigger (Redis removed)
    onUserPreferencesChange,
    // ✅ NEW: Smart coordination triggers for robust stats and DND coordination
    onDeliveryChangeSmartCoordination,
    onAddressManualDndChangeCoordination,
    // ✅ NEW: Shielded address validation functions
    validateDeliveryAddressFlow,
    getDeliveryEligibleAddressesFlow,
    checkAddressDeliveryStatusFlow
} from './triggers';

// Note: The actual flow definitions (standardizeDelivery, processPendingNotification, etc.)
// and simulation flows are now in their respective files within the 'flows' and 'simulations'
// directories. They are imported by 'triggers.ts' or 'local-runner.ts' as needed.
// The local runner script is now in 'local-runner.ts' and should be executed via
// `npm run build && node lib/local-runner.js`.
