import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';

// ✅ EFFICIENCY: Import minimal helpers
import { logEfficiencyGain } from '../utils/efficiency-helpers.js';

// ✅ ONLY use generated models from schemas
import type {
  Address
} from '../models/generated/address.schema';

import type {
  UserProfileSchema
} from '../models/generated/user_profile.schema';

const db = getFirestore();

// Input/Output schemas
const SetManualDndStateInputSchema = z.object({
  userId: z.string(),
  addressId: z.string(),
  desiredState: z.enum(['FORCE_DND', 'FORCE_ALLOW', 'AUTOMATIC'])
});

const SetManualDndStateOutputSchema = z.object({
  success: z.boolean(),
  quotaExceeded: z.boolean().optional(),
  currentQuota: z.object({
    used: z.number(),
    limit: z.number().nullable() // null = unlimited
  }).optional(),
  error: z.string().optional()
});

/**
 * ✅ FOCUSED FUNCTION: Set manual DND override state for an address
 * 
 * Purpose: Handle manual DND overrides with quota enforcement
 * Uses: Only generated schema models
 * Updates: Address manualDndState and user quota
 */
export const setManualDndStateFlow = defineFlow(
  {
    name: 'setManualDndState',
    inputSchema: SetManualDndStateInputSchema,
    outputSchema: SetManualDndStateOutputSchema,
  },
  async (input) => {
    const logPrefix = `[SetManualDndState][${input.userId}][${input.addressId}]`;
    const startTime = Date.now();
    console.log(`${logPrefix} Setting manual DND state to: ${input.desiredState}`);

    try {
      const transactionStartTime = Date.now();
      return await db.runTransaction(async (transaction) => {
        // Fetch current data including deliveries for permanent DND check
        const fetchStartTime = Date.now();
        const [addressDoc, userDoc, deliveriesSnapshot] = await Promise.all([
          transaction.get(db.doc(`users/${input.userId}/user_addresses/${input.addressId}`)),
          transaction.get(db.doc(`users/${input.userId}`)),
          transaction.get(db.collection(`users/${input.userId}/user_deliveries`)
            .where('deliveryData.reference.addressId', '==', input.addressId)
            .limit(10)) // Small sample to check for explicit import flags
        ]);

        const fetchDuration = Date.now() - fetchStartTime;
        const deliveryCount = deliveriesSnapshot.docs.length;
        const addressExists = addressDoc.exists;
        const userExists = userDoc.exists;

        console.log(`${logPrefix} Transaction data fetch completed in ${fetchDuration}ms - address: ${addressExists}, user: ${userExists}, deliveries: ${deliveryCount}`);

        if (!addressDoc.exists) {
          console.error(`${logPrefix} Address document not found`);
          throw new Error('Address not found');
        }

        const address: Address = addressDoc.data() as Address;
        const userProfile: UserProfileSchema = userDoc.exists ? userDoc.data() as UserProfileSchema : {} as UserProfileSchema;

        // ✅ CHECK FOR PERMANENT DND RESTRICTIONS (Free users cannot override explicit import DND)
        const deliveries = deliveriesSnapshot.docs.map(doc => doc.data());
        const hasExplicitImportDnd = deliveries.some(delivery => {
          const status = delivery.deliveryData?.status;
          return status?.dndReason === 'EXPLICIT_IMPORT';
        });

        const subscription = userProfile.subscription;
        const isPremium = subscription?.isActive === true;

        console.log(`${logPrefix} Validation checks - premium: ${isPremium}, explicitImport: ${hasExplicitImportDnd}, desiredState: ${input.desiredState}`);

        // ✅ PERMANENT DND RESTRICTION: Free users cannot override explicit import DND
        if (hasExplicitImportDnd && !isPremium && input.desiredState === "FORCE_ALLOW") {
          console.log(`${logPrefix} Free user attempting to override permanent DND - blocked`);
          return {
            success: false,
            quotaExceeded: false,
            error: "Cannot override permanent DND status. Upgrade to Premium to override imported DND flags.",
            currentQuota: {
              used: Number(userProfile.usage?.dndMarkingsUsed || 0),
              limit: 15
            }
          };
        }

        // Check current manual state
        const currentManualState = address.addressData?.flags?.manualDndState;
        const isCurrentlyManual = currentManualState === "FORCE_DND" || currentManualState === "FORCE_ALLOW";
        const willBeManual = input.desiredState === "FORCE_DND" || input.desiredState === "FORCE_ALLOW";

        console.log(`${logPrefix} Current: ${currentManualState}, Will be: ${input.desiredState}, hasExplicitImport: ${hasExplicitImportDnd}`);

        // Calculate quota impact
        let quotaDelta = 0;
        if (!isCurrentlyManual && willBeManual) {
          quotaDelta = 1; // Adding new manual override
        } else if (isCurrentlyManual && !willBeManual) {
          quotaDelta = -1; // Removing manual override
        }
        // If both manual or changing between manual states, no quota change

        // Check quota limits for non-premium users
        const currentQuotaUsed = Number(userProfile.usage?.dndMarkingsUsed || 0);
        const maxQuota = isPremium ? null : 15; // null = unlimited for premium

        console.log(`${logPrefix} Quota check - used: ${currentQuotaUsed}, delta: ${quotaDelta}, isPremium: ${isPremium}`);

        if (!isPremium && maxQuota && (currentQuotaUsed + quotaDelta) > maxQuota) {
          console.log(`${logPrefix} Quota exceeded`);
          return {
            success: false,
            quotaExceeded: true,
            currentQuota: {
              used: currentQuotaUsed,
              limit: maxQuota
            }
          };
        }

        // ✅ EFFICIENCY: Only update address if manual state actually changed
        const newManualState = input.desiredState === 'AUTOMATIC' ? null : input.desiredState;

        if (currentManualState !== newManualState) {
          transaction.update(addressDoc.ref, {
            'addressData.flags.manualDndState': newManualState,
            'addressData.metadata.updatedAt': FieldValue.serverTimestamp(),
            'addressData.metadata.lastManualDndUpdate': FieldValue.serverTimestamp()
          });
          console.log(`${logPrefix} Manual DND state updated: ${currentManualState} → ${newManualState}`);
        } else {
          logEfficiencyGain(logPrefix, 'address manual state update', true, 'manual state unchanged');
        }

        // Update user quota if needed
        if (quotaDelta !== 0) {
          transaction.update(userDoc.ref, {
            'usage.dndMarkingsUsed': FieldValue.increment(quotaDelta),
            'metadata.updatedAt': FieldValue.serverTimestamp()
          });
        }

        const transactionDuration = Date.now() - transactionStartTime;
        console.log(`${logPrefix} Transaction completed in ${transactionDuration}ms - quotaDelta: ${quotaDelta}, newUsed: ${currentQuotaUsed + quotaDelta}`);

        const totalDuration = Date.now() - startTime;
        console.log(`${logPrefix} Manual DND state update completed successfully in ${totalDuration}ms`);

        return {
          success: true,
          currentQuota: {
            used: currentQuotaUsed + quotaDelta,
            limit: maxQuota
          }
        };
      });

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      console.error(`${logPrefix} Error setting manual DND state after ${totalDuration}ms:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
);
