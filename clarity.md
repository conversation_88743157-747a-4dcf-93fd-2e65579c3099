# Clarity Architecture: A Guide to Robust and Maintainable Systems

**Core Principle:** Each component has a single, well-defined job.

**Goal:** Achieve maximum clarity, maintainability, testability, and scalability through strict adherence to Clean Architecture principles. This approach emphasizes a strong separation of concerns, with each component having a clear, unambiguous responsibility, making the system easier to understand, develop, and maintain, especially when leveraging LLM-assisted development.

## PRINCIPLE RULES

### Architectural Component Responsibilities

#### 1. DtoUtils.kt = PURE FIRESTORE UTILITIES + STRUCTURE ADAPTATION
✅ **BELONGS HERE:**
- documentSnapshotToAddressDto()
- documentSnapshotToDeliveryDto()
- documentSnapshotToUserProfileDto() - extracts from nested `profileData` structure
- wrapUserProfileDtoForFirestore() - wraps flat DTO in `profileData` structure
- wrapUserProfileFieldsForFirestore() - prefixes field paths with `profileData.`
- parseOffsetDateTime()
- addressDataToMap()
- putIfValueNotNull()
- toMap() extension functions
- Date/timestamp parsing utilities
- Raw DocumentSnapshot → DTO conversion
- Firestore document structure adaptation (nested ↔ flat)

#### 2. XXXMapper.kt = DTO↔SSoT + ENCRYPTION + BUSINESS LOGIC
✅ **BELONGS HERE:**
- mapToDomain() / mapToDto() / mapToDtoData()
- encryptField() / decryptField()
- calculateDeliveryStats()
- calculateTipPercentage()
- applyDoNotDeliverRules() ← MOVE FROM DtoUtils
- roundTo2DecimalPlaces() ← MOVE FROM DtoUtils
- createDefaultDelivery()
- transformUsageStatsToDeliveryStats()
- ALL domain business logic and calculations

#### 3. XXXRepositoryImpl.kt = DUAL INTERFACE ORCHESTRATION
✅ **BELONGS HERE:**
- **DUAL INTERFACE IMPLEMENTATION**: Implements both domain interface (UserRepository) AND data interface (UserProfileRepository)
- **DOMAIN OPERATIONS**: Business-focused methods for ViewModels and UseCases (via domain interface)
- **DATA OPERATIONS**: Comprehensive data management methods for infrastructure components (via data interface)
- Coordinating RemoteDataSource + LocalDataSource + Mapper
- Cache-first strategies
- Multi-step operations
- Error handling and Result wrapping
- Calling mapper.calculateXxx() methods

❌ **DOES NOT BELONG HERE:**
- Direct business calculations
- Direct encryption/decryption

### Repository Direct Component Injection (No Intermediate Layers)
Repository implementations must directly inject only RemoteDataSource, LocalDataSource, and Mapper components, eliminating intermediate access layers like FirestoreAccess or ModelTransformations classes. Following AddressRepositoryImpl (`data/repository/address/AddressRepositoryImpl.kt`), repositories coordinate data operations through these three core components only, with all Firestore operations handled within RemoteDataSource and all business transformations handled within Mapper or UseCases. This prevents architectural drift and maintains clear component boundaries.

### Dual Model System (DTO + SSoT)
Repositories must import both Generated DTOs (`data/model/generated_kt/`) and Domain Models (`domain/model/`) to serve as translation bridges. Generated DTOs handle Firestore serialization with encrypted PII fields, while Domain Models provide decrypted, business-logic-focused representations for application use. The repository layer uses mappers to translate between DTOs for persistence operations and SSoT models for local cache and business logic, ensuring clean separation between data storage and domain concerns.

### Dual Interface Orchestration Pattern (CRITICAL ARCHITECTURAL STANDARD)
Repository implementations MUST implement both domain and data layer interfaces to serve dual architectural purposes following the Cache System Boundary Adaptation Pattern from atomic-caching.md. This dual implementation enables proper separation between business operations (domain interface) and comprehensive data management operations (data interface).

**MANDATORY IMPLEMENTATION PATTERN:**
```kotlin
class UserProfileRepositoryImpl(
    // ... dependencies
) : UserRepository, UserProfileRepository {
    // Domain interface methods (for ViewModels, UseCases)
    override suspend fun getCurrentUser(): Result<User?> { /* business logic */ }
    override fun observeCurrentUser(): Flow<Result<User?>> { /* reactive business data */ }

    // Data interface methods (for infrastructure, caching, maintenance)
    override suspend fun clearAllCache(): Result<Unit> { /* comprehensive cache management */ }
    override suspend fun exportUserData(userId: String, format: String): Result<String> { /* data operations */ }
    override suspend fun prefetchUserData(userId: String): Result<Unit> { /* performance operations */ }
}
```

**DEPENDENCY INJECTION BINDING:**
```kotlin
// DataModule.kt - BOTH interfaces must be bound
single<UserRepository> { UserProfileRepositoryImpl(...) }
single<UserProfileRepository> { get<UserRepository>() as UserProfileRepositoryImpl }
```

**ARCHITECTURAL BENEFITS:**
- **Domain Interface**: Serves ViewModels and business logic with essential operations
- **Data Interface**: Serves CacheLifecycleManager, CacheWarmingManager, and system maintenance
- **Single Implementation**: Eliminates code duplication and maintains consistency
- **Clear Separation**: Business concerns vs. infrastructure concerns through different interfaces
- **Atomic Caching Integration**: Enables proper cache system boundary adaptation

### Repository "House All" Pattern
Repository implementations must consolidate all data access logic into a single class following the AddressRepositoryImpl pattern (`data/repository/address/AddressRepositoryImpl.kt`). Each repository coordinates between RemoteDataSource (Firestore operations), LocalDataSource (cache operations), and Mapper (DTO↔SSoT translation) components, returning Result<T> wrappers for consistent error handling. This eliminates scattered data access code and provides a unified interface for all CRUD operations, queries, and business-specific methods within a single, comprehensive implementation.

### DataSource Model Segregation (DTO vs SSoT)
LocalDataSource must operate exclusively with SSoT domain models (`domain/model/`), while RemoteDataSource must operate exclusively with Generated DTOs (`data/model/generated_kt/`). LocalDataSource interfaces with cache systems storing decrypted domain objects, while RemoteDataSource handles Firestore operations using encrypted DTOs with DtoUtils (`data/model/util_kt/DtoUtils.kt`) for DocumentSnapshot parsing and Firestore structure adaptation. DtoUtils serves as the structural bridge between Firestore's nested document format (e.g., `{profileData: {...}}`) and the application's flat DTO expectations, providing wrapper/unwrapper functions for seamless data layer integration. This strict segregation ensures clear data boundaries: encrypted persistence layer (Remote+DTO) and decrypted business layer (Local+SSoT).

## 1. Guiding Philosophy

This architecture prioritizes:

*   **Single Responsibility Principle (SRP):** Every class, module, or function should have responsibility over a single part of the application's functionality, and that responsibility should be entirely encapsulated by the class.
*   **Predictability:** The role and location of logic should be immediately obvious.
*   **Testability:** Small, focused components are inherently easier to unit test.
*   **Scalability & Maintainability:** Clear boundaries reduce the risk of unintended side effects when adding or modifying features.
*   **LLM Comprehension:** A highly structured and predictable system with clearly defined roles for each component is easier for Large Language Models to understand, navigate, and modify correctly.

## 2. Architectural Layers & Package Structure

The system is organized into distinct layers, primarily `domain` and `data`, promoting the Dependency Inversion Principle.

```
com.autogratuity
├── domain
│   ├── model               // Pure SSoT (Single Source of Truth) business objects/entities
│   ├── repository          // Interfaces defining contracts for data access
│   └── usecase             // Business logic orchestrators for specific operations
│
└── data
    ├── repository          // Implementations of domain repository interfaces; coordinators
    ├── datasource
    │   ├── remote          // Interfaces & Implementations for remote data (e.g., Firestore)
    │   └── local           // Interfaces & Implementations for local data (e.g., Cache)
    ├── model               // Data Transfer Objects (DTOs), e.g., for Firestore, network
    │   └── generated_kt    // Auto-generated models (e.g., from Firestore schema)
    ├── mapper              // (or transformer) For SSoT <--> DTO conversion & PII handling
    └── XxxTransactionManager // Domain-specific complex transaction handlers (lives alongside datasources)
```

## 3. Key Component Responsibilities

Each component type has a strict and singular purpose:

### 3.1. `domain.model.Xxx` (e.g., `Delivery`, `User`)
    *   **Responsibility:** Represent pure SSoT business entities and their intrinsic business rules.
    *   **Characteristics:**
        *   Pure Kotlin data classes or classes.
        *   No dependencies on Android SDK, database frameworks, or network libraries.
        *   Contain only data fields and methods relevant to the core business concept.
        *   Example: `data class Delivery(val id: String, val status: DeliveryStatus, ...)`

### 3.2. `domain.repository.XxxRepository` (e.g., `UserRepository`)
    *   **Responsibility:** Define the contract for how the domain layer fetches and stores its SSoT models. Serves as the business-focused interface for ViewModels and UseCases.
    *   **Characteristics:**
        *   Kotlin `interface` definitions.
        *   Methods operate *exclusively* with `domain.model.Xxx` SSoT objects.
        *   **Business-Focused Operations**: Essential CRUD operations, reactive streams, core business methods
        *   **Used By**: ViewModels, UseCases, business logic components
        *   Example: `interface UserRepository { suspend fun getCurrentUser(): Result<User?>; fun observeCurrentUser(): Flow<Result<User?>>; }`

### 3.2.1. `data.repository.xxx.XxxProfileRepository` (e.g., `UserProfileRepository`)
    *   **Responsibility:** Define the comprehensive contract for data layer operations including cache management, import/export, backup/recovery, and system maintenance operations.
    *   **Characteristics:**
        *   Kotlin `interface` definitions in data layer.
        *   Methods operate with `domain.model.Xxx` SSoT objects but focus on data management operations.
        *   **Infrastructure-Focused Operations**: Cache management, data export/import, backup/recovery, prefetching, system maintenance
        *   **Used By**: CacheLifecycleManager, CacheWarmingManager, system maintenance components, administrative tools
        *   **Comprehensive Coverage**: Includes all domain interface methods PLUS extensive data management methods
        *   Example: `interface UserProfileRepository : /* inherits domain methods */ { suspend fun clearAllCache(): Result<Unit>; suspend fun exportUserData(userId: String, format: String): Result<String>; }`

### 3.3. `domain.usecase.XxxUseCase` (e.g., `CompleteDeliveryUseCase`)
    *   **Responsibility:** Encapsulate specific, atomic business operations or user actions. Orchestrate repositories.
    *   **Characteristics:**
        *   Classes that typically handle one specific piece of functionality.
        *   Inject `domain.repository.XxxRepository` interfaces.
        *   Contain business logic that may span multiple entities or steps.
        *   Called by the presentation layer (e.g., ViewModels).
        *   Example: `class CompleteDeliveryUseCase(private val repo: DeliveryRepository) { suspend fun execute(deliveryId: String): Result<Unit> { /* ... */ } }`

### 3.4. `data.repository.XxxRepositoryImpl` (e.g., `UserProfileRepositoryImpl`)
    *   **Responsibility:** Implement BOTH the `domain.repository.XxxRepository` interface AND the `data.repository.xxx.XxxProfileRepository` interface. Coordinate data operations between local and remote data sources while serving dual architectural purposes.
    *   **Characteristics:**
        *   **DUAL INTERFACE IMPLEMENTATION**: Must implement both domain interface (for business operations) and data interface (for comprehensive data management)
        *   Injects `XxxRemoteDataSource`, `XxxLocalDataSource`, and `XxxMapper`.
        *   Contains logic for data fetching strategies (e.g., cache-then-network).
        *   **Domain Interface Methods**: Serve ViewModels, UseCases with essential business operations (getCurrentUser, observeCurrentUser, etc.)
        *   **Data Interface Methods**: Serve infrastructure components with comprehensive operations (clearAllCache, exportUserData, prefetchUserData, etc.)
        *   Does *not* directly interact with Firestore APIs, perform encryption/decryption, or contain complex business rules (those are in use cases or mappers).
        *   Deals with SSoT models when interacting with its contracts, but uses mappers to interact with datasources using DTOs.
        *   **MANDATORY PATTERN**: All repository implementations must follow this dual interface orchestration pattern for proper atomic caching system integration.

### 3.5. `data.mapper.XxxMapper` (or `XxxTransformer`)
    *   **Responsibility:** Bidirectional mapping between `domain.model.Xxx` (SSoT) and `data.model.XxxDto`. **Crucially, handles all PII encryption/decryption during this transformation.**
    *   **Characteristics:**
        *   Classes with methods like `fun mapToDomain(dto: XxxDto): Xxx` and `fun mapToDto(ssot: Xxx): XxxDto`.
        *   Encryption/decryption is an integral part of the mapping process to/from DTOs.
        *   Ensures data is in the correct format for persistence or network transfer and that SSoT models are pure.
        *   Must fail loudly and clearly if encryption/decryption operations fail (e.g., throw `CryptoException`).

### 3.6. `data.datasource.remote.XxxRemoteDataSource`
    *   **Responsibility:** All interactions with a specific remote data source (e.g., Firebase Firestore) for the `Xxx` domain.
    *   **Characteristics:**
        *   Interface and implementation.
        *   Operates *exclusively* with `data.model.XxxDto` objects.
        *   Handles query building, execution, and result parsing from the remote source.
        *   May interact with a domain-specific `XxxTransactionManager` for atomic writes.
        *   No business logic or model transformation (that's for mappers and use cases).

### 3.7. `data.datasource.local.XxxLocalDataSource`
    *   **Responsibility:** Caching and local persistence strategy for the `Xxx` domain.
    *   **Characteristics:**
        *   Interface and implementation.
        *   Operates **exclusively** with `domain.model.Xxx` SSoT (Single Source of Truth) objects. The cache stores decrypted, directly usable domain models.
        *   Implementations (e.g., `AddressLocalDataSourceImpl`) directly use a shared, robust, and SSoT-aware caching mechanism (e.g., `AtomicCacheSystem<K, SSoT_Value_Type>`) as an implementation detail.
        *   This layer does **not** perform any SSoT <-> DTO mapping; its contract is purely with SSoT models.
        *   Implements caching strategies (TTL, eviction policies) as configured within the underlying cache system or the data source itself.

### 3.8. `data.XxxTransactionManager`
    *   **Responsibility:** Exclusively execute complex Firestore transactions (`firestore.runTransaction` or `firestore.batch()`) that require atomicity across multiple documents or write operations.
    *   **Characteristics:**
        *   Lean, focused component.
        *   Receives `data.model.XxxDto` objects that are *already fully prepared* (encrypted, correctly typed) by the calling layer (typically `XxxRemoteDataSource` or orchestrated by `XxxRepositoryImpl` via the `XxxRemoteDataSource`).
        *   Contains no business logic, model transformation, or caching logic itself.

### 3.9. `data.model.XxxDto` & `data.model.generated_kt`
    *   **Responsibility:** Represent data structures for persistence (Firestore), network transfer, or other data sources.
    *   **Characteristics:**
        *   Can contain annotations specific to the data source (e.g., `@PropertyName` for Firestore).
        *   `generated_kt` models (e.g., from a schema tool) fit here.
        *   These models are *never* directly used by or returned from the `domain` layer or presentation layer.

### 3.10. Handling of UI State, DTO/Repository Utilities, and Intermediate Data Representations

It's common for projects to develop utility classes for DTO manipulation, repository operations, or models representing UI-specific states. The Clarity Architecture provides specific guidance:

*   **UI State Models (e.g., `AuthenticationStatus`, `DndDisplayState`):**
    *   **Responsibility:** Represent the state of the UI, ready for consumption by UI components.
    *   **Placement:** These models belong in the **presentation layer** (e.g., `com.autogratuity.ui.featureName.state` or within a ViewModel's scope). They are *not* part of the `data` or `domain` layers.
    *   **Rationale:** The `data` layer's responsibility ends with providing SSoT models (via `RepositoryImpl` and `Mapper`) or raw DTOs (within `DataSource`). The `domain` layer deals only with SSoT. Transforming SSoT models or data operation statuses (like `RepositoryInitializationStatus`) into UI-consumable state is a presentation layer concern.

*   **DTO-to-DTO Converters & Accessor Utilities (e.g., `ModelConverters.kt` content):**
    *   **Clarity Principle:** Avoid generalized "converter" or "utility" classes that handle multiple, unrelated DTO transformations or access patterns.
    *   **Preferred Approach:**
        *   If converting between different DTO versions or representations *within the `data.model` package* (and not involving SSoT models), this logic should ideally be:
            *   Part of the DTO itself (e.g., a constructor or a method like `fun toLegacyFormatDto(): LegacyDto`).
            *   A private/internal extension function co-located with the DTOs if the logic is simple and highly specific to those DTOs.
        *   Logic for accessing nested data within a DTO (like `getCustomData` in `ModelConverters.kt`) should typically be a direct property access on the DTO if the structure is stable, or a small helper function within the DTO's companion object if minor processing is needed.
    *   **Strict Avoidance:** Such utilities must *not* perform SSoT <-> DTO mapping; that is the sole responsibility of `XxxMapper` classes. They must also not handle PII.
    *   **Refactoring Goal:** Phase out broad utility classes like `ModelConverters.kt`. Their logic should be refactored into the DTOs themselves or into specific, narrowly-scoped helpers if absolutely necessary, ensuring they don't violate SRP or the clear responsibilities of Mappers.

*   **Interfacing with Auto-Generated DTOs (e.g., in `data.model.generated_kt`):**
    *   **Expectation:** Auto-generated DTOs are a starting point. Minor, well-commented manual adjustments to generated files may be needed initially (e.g., for type mismatches not caught by the generator). The generating schema remains the DTO's SSoT.
    *   **Mapper's Role:** The `XxxMapper` is the primary component for robustly bridging any structural or type differences between `data.model.generated_kt.XxxDto` and the `domain.model.Xxx` (SSoT).
    *   **Minimize Ad-hoc DTO-DTO Converters:** Avoid creating broad utility classes for numerous DTO-to-DTO transformations. Prefer improving code generation, enhancing mapper logic to handle variations, or defining clear, purposeful DTO variants with explicit conversion methods if truly necessary. The goal is to prevent a proliferation of intermediate conversion utilities outside the main `XxxMapper` SSoT<->DTO pathway.

*   **Low-Level Data Source Utilities (e.g., `FirestoreMapUtil.kt`):**
    *   **Responsibility:** Handle very low-level, generic tasks related to a specific data source's raw output before it's even a typed DTO (e.g., converting raw `Map<*, *>` from Firestore to `Map<String, Any?>`).
    *   **Placement:** Such utilities are acceptable if they are:
        *   Stateless and highly generic to the data source interaction.
        *   Used *exclusively* within `XxxRemoteDataSource` implementations during the initial phase of handling raw data from the external source (e.g., Firebase SDK).
    *   **Characteristics:**
        *   They should be narrowly focused. They should not contain any business logic, SSoT awareness, or PII handling. Consider making them `internal`.

*   **Repository Implementation Utilities & Extensions (e.g., `RxJavaRepositoryExtensions.kt` modernized to Flow extensions):**
    *   **Responsibility:** Provide reusable, standardized implementations for common repository concerns like caching strategies (cache-then-network), retry logic, or common Flow transformations applied within `XxxRepositoryImpl`.
    *   **Placement:** Can reside in a shared `utils` or `extensions` package within the `data.repository` layer (e.g., `com.autogratuity.data.repository.utils` or `com.autogratuity.data.repository.extensions`).
    *   **Characteristics:**
        *   Typically defined as extension functions (e.g., `Flow<T>.applyStandardRetry(...)`) or higher-order functions that encapsulate common patterns.
        *   They operate on or are used by `XxxRepositoryImpl` components and help reduce boilerplate within them.
        *   Should *not* contain domain-specific business logic but rather data access pattern logic.
    *   **Example:** Flow extensions for standardized caching, retry mechanisms, or logging for repository operations are acceptable here.

*   **Repository Constants (e.g., `RepositoryConstants.kt`):**
    *   **Responsibility:** Centralize constants used across multiple `XxxRepositoryImpl` or `XxxDataSource` components.
    *   **Placement:** Can reside in a shared `utils` or `common` package within the `data.repository` or `data.datasource` layers, or a general `data.common` if broadly used.
    *   **Characteristics:** Includes constants for cache keys, Firestore paths, error/logging tags, entity type strings, etc.
    *   **Benefit:** Promotes consistency and maintainability by avoiding magic strings/values scattered throughout the data layer.

*   **Operational Status Models (e.g., `RepositoryInitializationStatus`):**
    *   **Responsibility:** Represent the status or progress of an ongoing data layer operation.
    *   **Transmission & Consumption:** These can be emitted by `Repository` implementations (or `DataSources` if the operation is at that level) as part of a `Flow<DataResult<T>>` where `T` might be the status model itself, or as a separate `Flow<OperationStatus>`.
    *   **Clarity:** While defined based on data layer events, they act as communication objects to other layers. They should be clearly distinct from SSoT models or DTOs. They can reside in a shared `core` or `common` package within the `data` layer if used by multiple repositories, or be specific to a repository's interface/implementation package.

### 3.11. Example: Address Domain Implementation

This example shows how all layers integrate for the Address domain:

1. Domain Layer
   - Model: `com.autogratuity.domain.model.Address`
   - Repository Interface: `AddressRepository`

2. Data Layer
   - Mapper: `AddressMapper` handles SSoT ⇄ DTO conversion and PII encryption/decryption
   - RemoteDataSource: `AddressRemoteDataSource` encapsulates Firestore calls on DTOs
   - LocalDataSource: `AddressLocalDataSource` wraps `AddressCacheSystem` for SSoT caching

3. RepositoryImpl
   - `AddressRepositoryImpl` composes Remote + Local + Mapper
   - Exposes only SSoT models and `Result`/`Flow<Result>`
   - Coordinates multi-step operations (e.g. setDefaultAddress)

4. Caching Infrastructure

**Core Cache System**
- `AtomicCacheSystem<K, V>`: Generic, atomic, reactive, in-memory cache
- Implements `DomainCacheSystem<K,V>` with TTL, LRU eviction, metrics, Flow-based observation

**Address Cache System**
- `AddressCacheSystem` extends `AtomicCacheSystem<String, Address>`
- Constructs keys via `getAddressCacheKey(userId, addressId)` and `getAddressesCacheKey(userId)`
- Augments with domain metadata (e.g., DND flag, tip statistics, ssotFields)
- Provides specialized invalidation and retrieval methods (`invalidateUserAddresses`, `getTopTippingAddresses`, `updateFromCloudFunction`)

## General Cache Flow

The caching architecture follows a consistent pattern across all domains, providing cache-first data access with remote fallback:

```
AtomicCacheSystem<K, V> (Base)
    ↓ (extends)
DomainCacheSystem (Address/Delivery/etc.)
    ↓ (used by)
LocalDataSource
    ↓ (coordinates with)
RepositoryImpl
    ↓ (serves)
Domain Layer
```

**Flow Details:**
- **Cache-First Strategy**: LocalDataSource checks domain-specific cache system first
- **Remote Fallback**: On cache miss, RemoteDataSource fetches from Firestore
- **SSoT Caching**: Only decrypted SSoT models are cached, never raw DTOs
- **Reactive Updates**: Cache systems provide Flow-based observation for real-time UI updates
- **Domain Metadata**: Each cache system tracks domain-specific metadata for intelligent invalidation

## General Encryption Flow

All domains follow a standardized encryption pattern for PII protection, ensuring data is encrypted at rest and decrypted only when needed:

```
RemoteDataSource (Encrypted DTOs)
    ↓ (fetches)
Mapper.mapToDomain()
    ↓ (decrypts via encryptField/decryptField)
SSoT Models (Decrypted PII)
    ↓ (cached in)
DomainCacheSystem
    ↓ (served by)
LocalDataSource
```

**Encryption Details:**
- **PII Fields**: Address components, coordinates, notes, and other sensitive data are encrypted
- **Bidirectional Mapping**: `encryptField()` for SSoT→DTO, `decryptField()` for DTO→SSoT
- **Error Handling**: Comprehensive `CryptoResult` handling with fallback strategies
- **Cache Security**: Only decrypted SSoT models are cached in memory for performance
- **Field Detection**: Automatic detection of encrypted vs plaintext data for legacy compatibility

This example demonstrates how to build domain-specific caches on top of a reusable core, a pattern to be mirrored in User and Delivery domains.

## 4. Dual Interface Implementation Requirements

### 4.1. Technical Implementation Standards

**MANDATORY IMPLEMENTATION CHECKLIST:**

1. **Class Declaration:**
   ```kotlin
   class XxxRepositoryImpl(...) : DomainXxxRepository, DataXxxRepository
   ```

2. **Method Implementation Strategy:**
   - **Domain methods**: Focus on business logic, essential operations
   - **Data methods**: Focus on infrastructure, comprehensive data management
   - **Shared logic**: Domain methods can delegate to data methods when appropriate
   - **No duplication**: Avoid implementing identical logic twice

3. **Dependency Injection Configuration:**
   ```kotlin
   // BOTH interfaces must be bound in DI
   single<DomainXxxRepository> { XxxRepositoryImpl(...) }
   single<DataXxxRepository> { get<DomainXxxRepository>() as XxxRepositoryImpl }
   ```

4. **Interface Method Categories:**
   - **Domain Interface**: getCurrentUser(), observeCurrentUser(), addUser(), updateUser(), deleteUser()
   - **Data Interface**: clearAllCache(), exportUserData(), prefetchUserData(), createUserBackup(), migrateUserData()

5. **Error Handling:**
   - Both interfaces use consistent `Result<T>` and `Flow<Result<T>>` patterns
   - Comprehensive error handling with proper exception types
   - Graceful degradation for infrastructure operations

### 4.2. Architectural Integration Points

**INFRASTRUCTURE COMPONENT USAGE:**
- **CacheLifecycleManager**: Uses data interface for `cleanup()`, `clearAllCache()`
- **CacheWarmingManager**: Uses data interface for `prefetchUserData()`
- **System Maintenance**: Uses data interface for `exportUserData()`, `createUserBackup()`
- **ViewModels**: Use domain interface for `getCurrentUser()`, `observeCurrentUser()`
- **UseCases**: Use domain interface for business operations

**ATOMIC CACHING INTEGRATION:**
- Data interface methods integrate with AtomicCacheSystem and UserProfileCacheSystem
- Cache operations are exposed through data interface for infrastructure control
- Domain interface methods trigger cache warming and management internally

## 5. Data Flow & Asynchronicity

*   **Primary Data Flow (Business Operations via Domain Interface):**
    `UI/ViewModel` -> `XxxUseCase` -> `domain.XxxRepository` (IF) -> `data.XxxRepositoryImpl` (domain methods)
    `data.XxxRepositoryImpl` -> `data.XxxLocalDataSource` (Cache check for SSoT `Xxx`)
    (If cache miss/stale) `data.XxxRepositoryImpl` -> `data.XxxRemoteDataSource`
    `data.XxxRemoteDataSource` -> (Firestore/Network) -> Returns `XxxDto`
    `data.XxxRepositoryImpl` -> `data.XxxMapper` (transforms `XxxDto` to SSoT `Xxx`)
    `data.XxxRepositoryImpl` -> (updates `data.XxxLocalDataSource` with the SSoT `Xxx` model)
    `data.XxxRepositoryImpl` -> Returns SSoT `Xxx` (or `Flow<DataResult<Xxx>>`) up the chain.

*   **Infrastructure Data Flow (System Operations via Data Interface):**
    `CacheLifecycleManager` -> `data.XxxProfileRepository` (IF) -> `data.XxxRepositoryImpl` (data methods)
    `CacheWarmingManager` -> `data.XxxProfileRepository` (IF) -> `data.XxxRepositoryImpl` (data methods)
    `SystemMaintenance` -> `data.XxxProfileRepository` (IF) -> `data.XxxRepositoryImpl` (data methods)
    `data.XxxRepositoryImpl` -> Comprehensive data operations (cache management, export/import, backup/recovery)
    `data.XxxRepositoryImpl` -> Returns operation results for infrastructure coordination

*   **Asynchronous API:**
    *   Use `kotlinx.coroutines.flow.Flow` for reactive streams of data.
    *   Employ a sealed `DataResult<T>` wrapper for `Flow` emissions from repositories/use cases to clearly represent `Loading`, `Success(data: T)`, and `Error(exception: Exception)` states for the UI.
    *   Use `Result<T>` for one-shot suspend function return types.

## 5. Critical Considerations

*   **Encryption:** All PII encryption/decryption is the explicit and sole responsibility of the `data.mapper.XxxMapper` during the SSoT <-> DTO transformation.
*   **Error Handling:** Consistent use of `Result<T>` and `DataResult<T>` for robust error propagation. Define specific, meaningful exceptions for different error categories (e.g., `CryptoException`, `NetworkException`, `DataNotFoundException`).
*   **Timestamp Handling:** Standardize on `java.time.OffsetDateTime` or similar for SSoT models. Mappers will handle conversion to/from ISO 8601 strings or Firestore Timestamps when interacting with DTOs/Firestore.
*   **Dependency Injection:** Use Hilt or a similar DI framework to provide dependencies.

## 6. LLM Interaction Strategy

*   **Targeted Prompts:** Direct LLMs to specific component types for specific tasks.
    *   "Implement the encryption for `User.phoneNumber` in `UserMapper.kt`."
    *   "Add a Firestore query to `DeliveryRemoteDataSource.kt` to fetch deliveries by status."
    *   "Create a new use case `FinalizeUserSubscriptionUseCase.kt` that interacts with `UserRepository` and `SubscriptionRepository`."
    *   **"Implement dual interface pattern for `XxxRepositoryImpl` with both domain and data interfaces."**
*   **Dual Interface Guidance:** When creating or modifying repository implementations:
    *   "Ensure `XxxRepositoryImpl` implements both `domain.XxxRepository` and `data.xxx.XxxProfileRepository`"
    *   "Add comprehensive data management methods to the data interface (cache, export, backup operations)"
    *   "Configure DI bindings for both interfaces pointing to the same implementation instance"
    *   "Separate business operations (domain interface) from infrastructure operations (data interface)"
*   **Pattern Reinforcement:** Provide examples of existing components adhering to this architecture when asking LLMs to create new ones.
*   **Contextual Scaffolding:** When asking for a new domain's data layer, provide the full package structure and empty file templates for each required component.

## 7. Phased Implementation & Governance

*   **Pilot Domain:** Select one domain (e.g., `delivery`) to refactor first to this new architecture. This will serve as a concrete template.
*   **Critical Fixes First:** Address PII encryption safety and consistent error handling across the board as a top priority, even before full refactoring of a domain.
*   **Iterative Refactoring:** Refactor other domains one by one.
*   **This Document as Golden Standard:** `clarity.md` becomes the definitive architectural guide.
*   **Code Reviews:** Strictly enforce adherence to these architectural principles.
*   **CI/CD Checks:** Where possible, implement linting or static analysis rules to detect violations of architectural boundaries.

This architecture, by design, creates more files per logical domain. However, the extreme clarity of responsibility for each file type is intended to make the overall system *more understandable and manageable*, especially for LLMs that thrive on predictable patterns. It directly combats the "mess" of overlapping responsibilities by enforcing a clean separation.
