import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';

// ✅ EFFICIENCY: Import minimal helpers
import { hasRealUserUpdates, logEfficiencyGain } from '../utils/efficiency-helpers.js';

// ✅ ONLY use generated models from schemas
import type {
  UserProfileSchema
} from '../models/generated/user_profile.schema';

const db = getFirestore();

// Input/Output schemas
const UpdateUserStatsInputSchema = z.object({
  userId: z.string(),
  deltas: z.object({
    deliveryCount: z.number().optional(),
    tipCount: z.number().optional(),
    totalTips: z.number().optional(),
    addressCount: z.number().optional(),
    dndMarkingsUsed: z.number().optional(),
    // ✅ NEW: DND change tracking
    dndCreated: z.boolean().optional(),
    dndRemoved: z.boolean().optional()
  }),
  // ✅ NEW: Coordination context
  coordinationContext: z.object({
    operationType: z.string().optional(),
    sourceFunction: z.string().optional(),
    version: z.number().optional()
  }).optional()
});

const UpdateUserStatsOutputSchema = z.object({
  success: z.boolean(),
  updatedStats: z.object({
    deliveryCount: z.number(),
    tipCount: z.number(),
    totalTips: z.number(),
    dndMarkingsUsed: z.number()
  }).optional(),
  error: z.string().optional()
});

/**
 * ✅ FOCUSED FUNCTION: Update user profile statistics
 * 
 * Purpose: Apply incremental updates to user usage statistics
 * Uses: Only generated schema models
 * Updates: User profile usage stats only
 */
export const updateUserStatsFlow = defineFlow(
  {
    name: 'updateUserStats',
    inputSchema: UpdateUserStatsInputSchema,
    outputSchema: UpdateUserStatsOutputSchema,
  },
  async (input) => {
    const logPrefix = `[UpdateUserStats][${input.userId}]`;
    const startTime = Date.now();
    const coordinationContext = input.coordinationContext || {};
    const deltaCount = Object.keys(input.deltas).filter(key => input.deltas[key as keyof typeof input.deltas] !== 0).length;
    console.log(`${logPrefix} Updating user stats with ${deltaCount} deltas:`, input.deltas);
    console.log(`${logPrefix} Coordination context:`, coordinationContext);

    try {
      const transactionStartTime = Date.now();
      return await db.runTransaction(async (transaction) => {
        // Get current user profile
        const fetchStartTime = Date.now();
        const userDoc = await transaction.get(db.doc(`users/${input.userId}`));
        const fetchDuration = Date.now() - fetchStartTime;

        const userExists = userDoc.exists;
        console.log(`${logPrefix} User fetch completed in ${fetchDuration}ms - exists: ${userExists}`);

        if (!userDoc.exists) {
          // Create basic user profile if it doesn't exist
          const newUserProfile: Partial<UserProfileSchema> = {
            userId: input.userId,
            usage: {
              deliveryCount: 0,
              tipCount: 0,
              totalTips: 0,
              dndMarkingsUsed: 0
            },
            usageStats: {
              totalRuns: 0,
              activeDaysCount: 0,
              totalTips: 0,
              featureUsage: {}
            },
            metadata: {
              createdAt: FieldValue.serverTimestamp() as any,
              updatedAt: FieldValue.serverTimestamp() as any
            }
          };

          transaction.set(userDoc.ref, newUserProfile);
          console.log(`${logPrefix} Created new user profile with initial stats`);
        }

        // ✅ EFFICIENCY: Build update object with increments (no metadata yet)
        const updateData: any = {};

        // Apply deltas using FieldValue.increment
        if (input.deltas.deliveryCount !== undefined && input.deltas.deliveryCount !== 0) {
          updateData['usage.deliveryCount'] = FieldValue.increment(input.deltas.deliveryCount);
          updateData['usageStats.totalRuns'] = FieldValue.increment(1); // Track function runs
        }

        if (input.deltas.tipCount !== undefined && input.deltas.tipCount !== 0) {
          // Note: Schema doesn't have tipCount in usage, using custom field
          updateData['usage.tipCount'] = FieldValue.increment(input.deltas.tipCount);
        }

        if (input.deltas.totalTips !== undefined && input.deltas.totalTips !== 0) {
          updateData['usageStats.totalTips'] = FieldValue.increment(input.deltas.totalTips);
        }

        if (input.deltas.addressCount !== undefined && input.deltas.addressCount !== 0) {
          updateData['usage.addressCount'] = FieldValue.increment(input.deltas.addressCount);
        }

        if (input.deltas.dndMarkingsUsed !== undefined && input.deltas.dndMarkingsUsed !== 0) {
          // Note: Custom field not in schema, but needed for DND quota tracking
          updateData['usage.dndMarkingsUsed'] = FieldValue.increment(input.deltas.dndMarkingsUsed);
        }

        // ✅ NEW: Track DND creation/removal for freemium quota enforcement
        if (input.deltas.dndCreated) {
          updateData['usageStats.featureUsage.DND_CREATED'] = FieldValue.increment(1);
          updateData['usage.dndMarkingsUsed'] = FieldValue.increment(1);
        }

        if (input.deltas.dndRemoved) {
          updateData['usageStats.featureUsage.DND_REMOVED'] = FieldValue.increment(1);
          // Note: Don't decrement dndMarkingsUsed - quota is cumulative
        }

        // ✅ SHIELD: Skip updates for default address operations
        if (coordinationContext.operationType === 'default_address_shielded') {
          console.log(`${logPrefix} DEFAULT ADDRESS SHIELDED - Skipping user stats update`);
          return { success: true, message: 'Default address shielded from stats' };
        }

        // ✅ ENHANCED: Daily tracking with coordination context
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
        updateData[`usageStats.featureUsage.daily_${today}`] = FieldValue.increment(1);

        // Track operation type for analytics
        if (coordinationContext.operationType) {
          updateData[`usageStats.featureUsage.${coordinationContext.operationType.toUpperCase()}`] = FieldValue.increment(1);
        }

        // ✅ EFFICIENCY: Only apply updates if there are real changes
        if (hasRealUserUpdates(updateData)) {
          // Add metadata only when there are real updates
          updateData['metadata.updatedAt'] = FieldValue.serverTimestamp();
          updateData['metadata.version'] = FieldValue.increment(1);

          const updateStartTime = Date.now();
          transaction.update(userDoc.ref, updateData);
          const updateDuration = Date.now() - updateStartTime;

          console.log(`${logPrefix} User stats updated with real changes - applied ${Object.keys(updateData).length} fields`);
        } else {
          logEfficiencyGain(logPrefix, 'user stats update', true, 'no real stat changes detected');
        }

        const transactionDuration = Date.now() - transactionStartTime;

        // Get updated values for response (approximate)
        const currentUser: UserProfileSchema = userDoc.exists ? userDoc.data() as UserProfileSchema : {} as UserProfileSchema;
        // Note: Schema UserUsage only has deliveryCount, addressCount, mappingCount
        // We'll use usageStats for tip-related data
        const currentUsage = currentUser.usage || {};
        const currentUsageStats = currentUser.usageStats || {};

        const totalDuration = Date.now() - startTime;
        console.log(`${logPrefix} User stats update completed successfully in ${totalDuration}ms`);

        return {
          success: true,
          updatedStats: {
            deliveryCount: (currentUsage.deliveryCount || 0) + (input.deltas.deliveryCount || 0),
            tipCount: (currentUsageStats.deliveryCount || 0) + (input.deltas.tipCount || 0), // Using deliveryCount as proxy
            totalTips: (currentUsageStats.totalTips || 0) + (input.deltas.totalTips || 0),
            dndMarkingsUsed: ((currentUsage as any).dndMarkingsUsed || 0) + (input.deltas.dndMarkingsUsed || 0) // Custom field
          }
        };
      });

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      console.error(`${logPrefix} Error updating user stats after ${totalDuration}ms:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
);
